/**
 * 翻译质量检查和AI翻译验证工具
 * 提供翻译质量评估、AI翻译验证和质量基准对比功能
 */

import { Locale } from '@/types/i18n';
import {
    QualityIssue,
    QualityScore,
    TranslationManager,
    TranslationManagerConfig,
    ValidationReport
} from './translation-manager';

// AI翻译服务接口
export interface AITranslationService {
  name: string;
  translate(text: string, fromLocale: Locale, toLocale: Locale): Promise<string>;
  batchTranslate(texts: string[], fromLocale: Locale, toLocale: Locale): Promise<string[]>;
}

// 翻译质量基准
export interface QualityBenchmark {
  locale: Locale;
  averageScore: number;
  benchmarkDate: string;
  sampleSize: number;
  categories: {
    grammar: number;
    consistency: number;
    terminology: number;
    fluency: number;
  };
}

// 质量对比结果
export interface QualityComparison {
  current: QualityScore;
  benchmark: QualityBenchmark;
  improvement: number; // 相对于基准的改进百分比
  recommendations: string[];
}

/**
 * 翻译质量分析器
 */
export class TranslationQualityAnalyzer {
  private translationManager: TranslationManager;
  private benchmarks: Map<Locale, QualityBenchmark> = new Map();

  constructor(config: TranslationManagerConfig) {
    this.translationManager = new TranslationManager(config);
  }

  /**
   * 初始化质量分析器
   */
  async initialize(): Promise<void> {
    await this.translationManager.initialize();
    await this.loadQualityBenchmarks();
  }

  /**
   * 验证AI翻译质量
   */
  async validateAITranslation(
    key: string,
    originalText: string,
    aiTranslation: string,
    targetLocale: Locale,
    humanTranslation?: string
  ): Promise<QualityScore> {
    const issues: QualityIssue[] = [];
    let score = 100;

    // 1. 基础验证
    const basicValidation = this.performBasicValidation(originalText, aiTranslation);
    issues.push(...basicValidation.issues);
    score -= basicValidation.penalty;

    // 2. 语言特定验证
    const languageValidation = this.performLanguageSpecificValidation(
      aiTranslation,
      targetLocale
    );
    issues.push(...languageValidation.issues);
    score -= languageValidation.penalty;

    // 3. 上下文一致性验证
    const contextValidation = await this.validateContextConsistency(
      key,
      aiTranslation,
      targetLocale
    );
    issues.push(...contextValidation.issues);
    score -= contextValidation.penalty;

    // 4. 与人工翻译对比（如果有）
    if (humanTranslation) {
      const humanComparison = this.compareWithHumanTranslation(
        aiTranslation,
        humanTranslation
      );
      issues.push(...humanComparison.issues);
      score -= humanComparison.penalty;
    }

    return {
      score: Math.max(0, score),
      confidence: this.calculateConfidence(issues, !!humanTranslation),
      issues,
      suggestions: this.generateImprovementSuggestions(issues),
    };
  }

  /**
   * 批量验证翻译质量
   */
  async batchValidateTranslations(
    translations: Array<{
      key: string;
      original: string;
      translated: string;
      locale: Locale;
      human?: string;
    }>
  ): Promise<Map<string, QualityScore>> {
    const results = new Map<string, QualityScore>();

    for (const translation of translations) {
      const score = await this.validateAITranslation(
        translation.key,
        translation.original,
        translation.translated,
        translation.locale,
        translation.human
      );

      results.set(translation.key, score);
    }

    return results;
  }

  /**
   * 与质量基准对比
   */
  async compareWithBenchmark(
    currentScore: QualityScore,
    locale: Locale
  ): Promise<QualityComparison> {
    const benchmark = this.benchmarks.get(locale);

    if (!benchmark) {
      throw new Error(`No quality benchmark found for locale: ${locale}`);
    }

    const improvement = ((currentScore.score - benchmark.averageScore) / benchmark.averageScore) * 100;

    const recommendations = this.generateBenchmarkRecommendations(
      currentScore,
      benchmark,
      improvement
    );

    return {
      current: currentScore,
      benchmark,
      improvement,
      recommendations,
    };
  }

  /**
   * 生成质量报告
   */
  async generateQualityReport(_locale?: Locale): Promise<ValidationReport> {
    return await this.translationManager.validateTranslationConsistency({});
  }

  /**
   * 基础验证
   */
  private performBasicValidation(
    original: string,
    translation: string
  ): { issues: QualityIssue[]; penalty: number } {
    const issues: QualityIssue[] = [];
    let penalty = 0;

    // 检查空翻译
    if (!translation || translation.trim().length === 0) {
      issues.push({
        type: 'length',
        severity: 'critical',
        message: 'Translation is empty',
        suggestion: 'Provide a non-empty translation',
      });
      penalty += 50;
    }

    // 检查是否与原文相同（可能未翻译）
    if (original === translation && original.length > 10) {
      issues.push({
        type: 'consistency',
        severity: 'high',
        message: 'Translation appears to be identical to source text',
        suggestion: 'Ensure the text has been properly translated',
      });
      penalty += 30;
    }

    // 检查长度比例
    if (translation.length > 0) {
      const lengthRatio = translation.length / original.length;
      if (lengthRatio > 4 || lengthRatio < 0.2) {
        issues.push({
          type: 'length',
          severity: 'medium',
          message: `Translation length ratio is unusual: ${lengthRatio.toFixed(2)}`,
          suggestion: 'Review translation for completeness and accuracy',
        });
        penalty += 15;
      }
    }

    // 检查占位符
    const originalPlaceholders = this.extractPlaceholders(original);
    const translationPlaceholders = this.extractPlaceholders(translation);

    if (JSON.stringify(originalPlaceholders.sort()) !== JSON.stringify(translationPlaceholders.sort())) {
      issues.push({
        type: 'placeholder',
        severity: 'high',
        message: 'Placeholder mismatch between source and translation',
        suggestion: 'Ensure all placeholders are preserved in translation',
      });
      penalty += 25;
    }

    return { issues, penalty };
  }

  /**
   * 语言特定验证
   */
  private performLanguageSpecificValidation(
    translation: string,
    _locale: Locale
  ): { issues: QualityIssue[]; penalty: number } {
    const issues: QualityIssue[] = [];
    let penalty = 0;

    switch (_locale) {
      case 'zh':
        // 中文特定检查
        if (this.containsTraditionalChinese(translation)) {
          issues.push({
            type: 'consistency',
            severity: 'medium',
            message: 'Translation contains traditional Chinese characters',
            suggestion: 'Use simplified Chinese characters for consistency',
          });
          penalty += 10;
        }
        break;

      case 'en':
        // 英文特定检查
        if (this.hasGrammarIssues(translation)) {
          issues.push({
            type: 'grammar',
            severity: 'medium',
            message: 'Potential grammar issues detected',
            suggestion: 'Review grammar and sentence structure',
          });
          penalty += 10;
        }
        break;
    }

    return { issues, penalty };
  }

  /**
   * 验证上下文一致性
   */
  private async validateContextConsistency(
    key: string,
    translation: string,
    locale: Locale
  ): Promise<{ issues: QualityIssue[]; penalty: number }> {
    const issues: QualityIssue[] = [];
    let penalty = 0;

    // 检查术语一致性
    const terminologyIssues = await this.checkTerminologyConsistency(
      key,
      translation,
      locale
    );
    issues.push(...terminologyIssues);
    penalty += terminologyIssues.length * 5;

    // 检查上下文相关性
    const contextIssues = this.checkContextRelevance(key, translation);
    issues.push(...contextIssues);
    penalty += contextIssues.length * 8;

    return { issues, penalty };
  }

  /**
   * 与人工翻译对比
   */
  private compareWithHumanTranslation(
    aiTranslation: string,
    humanTranslation: string
  ): { issues: QualityIssue[]; penalty: number } {
    const issues: QualityIssue[] = [];
    let penalty = 0;

    // 计算相似度
    const similarity = this.calculateSimilarity(aiTranslation, humanTranslation);

    if (similarity < 0.3) {
      issues.push({
        type: 'consistency',
        severity: 'medium',
        message: `Low similarity with human translation: ${(similarity * 100).toFixed(1)}%`,
        suggestion: 'Review translation against human reference',
      });
      penalty += 15;
    }

    // 检查关键术语
    const humanTerms = this.extractKeyTerms(humanTranslation);
    const aiTerms = this.extractKeyTerms(aiTranslation);

    const missingTerms = humanTerms.filter(term => !aiTerms.includes(term));
    if (missingTerms.length > 0) {
      issues.push({
        type: 'terminology',
        severity: 'medium',
        message: `Missing key terms: ${missingTerms.join(', ')}`,
        suggestion: 'Include important terminology from human reference',
      });
      penalty += missingTerms.length * 5;
    }

    return { issues, penalty };
  }

  /**
   * 加载质量基准
   */
  private async loadQualityBenchmarks(): Promise<void> {
    // 这里可以从文件或API加载质量基准数据
    // 暂时使用默认基准
    this.benchmarks.set('en', {
      locale: 'en',
      averageScore: 85,
      benchmarkDate: '2024-01-01',
      sampleSize: 1000,
      categories: {
        grammar: 90,
        consistency: 85,
        terminology: 80,
        fluency: 88,
      },
    });

    this.benchmarks.set('zh', {
      locale: 'zh',
      averageScore: 82,
      benchmarkDate: '2024-01-01',
      sampleSize: 800,
      categories: {
        grammar: 88,
        consistency: 80,
        terminology: 78,
        fluency: 85,
      },
    });
  }

  /**
   * 提取占位符
   */
  private extractPlaceholders(text: string): string[] {
    const matches = text.match(/\{[^}]+\}/g) || [];
    return matches.map(match => match.slice(1, -1));
  }

  /**
   * 检查是否包含繁体中文
   */
  private containsTraditionalChinese(text: string): boolean {
    // 简单的繁体字检测（实际应用中可以使用更完善的库）
    const traditionalChars = /[繁體字檢測]/;
    return traditionalChars.test(text);
  }

  /**
   * 检查语法问题
   */
  private hasGrammarIssues(text: string): boolean {
    // 简单的语法检查（实际应用中可以集成语法检查库）
    const commonIssues = /\b(a a|the the|and and|or or)\b/i;
    return commonIssues.test(text);
  }

  /**
   * 检查术语一致性
   */
  private async checkTerminologyConsistency(
    _key: string,
    _translation: string,
    _locale: Locale
  ): Promise<QualityIssue[]> {
    // 这里可以实现术语词典检查
    return [];
  }

  /**
   * 检查上下文相关性
   */
  private checkContextRelevance(key: string, translation: string): QualityIssue[] {
    const issues: QualityIssue[] = [];

    // 根据键名检查上下文相关性
    if (key.includes('error') && !this.containsErrorTerms(translation)) {
      issues.push({
        type: 'context',
        severity: 'medium',
        message: 'Translation may not match error context',
        suggestion: 'Ensure translation is appropriate for error messages',
      });
    }

    return issues;
  }

  /**
   * 检查是否包含错误相关术语
   */
  private containsErrorTerms(text: string): boolean {
    const errorTerms = ['error', 'failed', 'invalid', '错误', '失败', '无效'];
    return errorTerms.some(term => text.toLowerCase().includes(term.toLowerCase()));
  }

  /**
   * 计算文本相似度
   */
  private calculateSimilarity(text1: string, text2: string): number {
    // 简单的相似度计算（实际应用中可以使用更复杂的算法）
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);

    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];

    return intersection.length / union.length;
  }

  /**
   * 提取关键术语
   */
  private extractKeyTerms(text: string): string[] {
    // 简单的关键术语提取（实际应用中可以使用NLP库）
    return text.split(/\s+/).filter(word => word.length > 3);
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(issues: QualityIssue[], hasHumanReference: boolean): number {
    let confidence = 0.8; // 基础置信度

    if (hasHumanReference) {
      confidence += 0.2; // 有人工参考提高置信度
    }

    const criticalIssues = issues.filter(issue => issue.severity === 'critical').length;
    const highIssues = issues.filter(issue => issue.severity === 'high').length;

    confidence -= criticalIssues * 0.3;
    confidence -= highIssues * 0.15;
    confidence -= issues.length * 0.05;

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * 生成改进建议
   */
  private generateImprovementSuggestions(issues: QualityIssue[]): string[] {
    const suggestions = new Set<string>();

    issues.forEach(issue => {
      if (issue.suggestion) {
        suggestions.add(issue.suggestion);
      }
    });

    // 添加通用建议
    if (issues.length > 5) {
      suggestions.add('Consider reviewing translation guidelines and best practices');
    }

    return Array.from(suggestions);
  }

  /**
   * 生成基准对比建议
   */
  private generateBenchmarkRecommendations(
    current: QualityScore,
    _benchmark: QualityBenchmark,
    improvement: number
  ): string[] {
    const recommendations: string[] = [];

    if (improvement < -10) {
      recommendations.push('Translation quality is significantly below benchmark');
      recommendations.push('Consider additional quality assurance measures');
    } else if (improvement < 0) {
      recommendations.push('Translation quality is slightly below benchmark');
      recommendations.push('Focus on addressing high-priority issues');
    } else if (improvement > 10) {
      recommendations.push('Translation quality exceeds benchmark standards');
      recommendations.push('Consider updating benchmark with current performance');
    }

    if (current.issues.length > 0) {
      recommendations.push(`Address ${current.issues.length} identified quality issues`);
    }

    return recommendations;
  }
}
