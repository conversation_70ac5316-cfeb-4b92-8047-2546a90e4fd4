/**
 * 翻译管理核心类
 * 包含Lingo.dev集成接口和翻译质量管理
 */

import { Locale } from '@/types/i18n';

// 翻译质量评分接口
export interface QualityScore {
  score: number; // 0-100
  confidence: number; // 0-1
  issues: QualityIssue[];
  suggestions: string[];
}

// 质量问题接口
export interface QualityIssue {
  type: 'grammar' | 'consistency' | 'terminology' | 'length' | 'placeholder' | 'context';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  suggestion?: string;
}

// 验证报告接口
export interface ValidationReport {
  isValid: boolean;
  score: number;
  issues: QualityIssue[];
  recommendations: string[];
  timestamp: string;
}

// 质量报告接口
export interface QualityReport {
  overall: QualityScore;
  byLocale: Record<Locale, QualityScore>;
  trends: QualityTrend[];
  recommendations: string[];
  timestamp: string;
}

// 质量趋势接口
export interface QualityTrend {
  date: string;
  locale: Locale;
  score: number;
  keyCount: number;
}

// Lingo.dev集成配置
export interface LingoConfig {
  apiKey?: string;
  projectId?: string;
  baseUrl?: string;
  enabled: boolean;
}

// 翻译管理配置
export interface TranslationManagerConfig {
  locales: Locale[];
  defaultLocale: Locale;
  messagesDir: string;
  lingo: LingoConfig;
  qualityThresholds: {
    minScore: number;
    maxIssues: number;
    criticalIssueThreshold: number;
  };
}

/**
 * 翻译质量检查接口
 */
export interface TranslationQualityCheck {
  checkLingoTranslation(
    key: string,
    aiTranslation: string,
    humanTranslation?: string
  ): Promise<QualityScore>;

  validateTranslationConsistency(
    translations: Record<string, string>
  ): Promise<ValidationReport>;

  generateQualityReport(): Promise<QualityReport>;
}

/**
 * 翻译管理器核心类
 */
export class TranslationManager implements TranslationQualityCheck {
  private config: TranslationManagerConfig;
  private translations: Partial<Record<Locale, Record<string, unknown>>> = {};
  private qualityCache: Map<string, QualityScore> = new Map();

  constructor(config: TranslationManagerConfig) {
    this.config = config;
  }

  /**
   * 初始化翻译管理器
   */
  async initialize(): Promise<void> {
    await this.loadTranslations();

    if (this.config.lingo.enabled) {
      await this.initializeLingoIntegration();
    }
  }

  /**
   * 加载翻译文件
   */
  private async loadTranslations(): Promise<void> {
    const fs = await import('fs');
    const path = await import('path');

    for (const locale of this.config.locales) {
      const filePath = path.join(this.config.messagesDir, `${locale}.json`);

      try {
        const content = fs.readFileSync(filePath, 'utf8');
        this.translations[locale] = JSON.parse(content);
      } catch (error) {
        console.warn(`Failed to load translations for ${locale}:`, error);
        this.translations[locale] = {};
      }
    }
  }

  /**
   * 初始化Lingo.dev集成
   */
  private async initializeLingoIntegration(): Promise<void> {
    if (!this.config.lingo.apiKey || !this.config.lingo.projectId) {
      console.warn('Lingo.dev integration enabled but missing API key or project ID');
      return;
    }

    // 这里可以添加Lingo.dev API的初始化逻辑
    console.log('Lingo.dev integration initialized');
  }

  /**
   * 检查Lingo.dev翻译质量
   */
  async checkLingoTranslation(
    key: string,
    aiTranslation: string,
    humanTranslation?: string
  ): Promise<QualityScore> {
    const cacheKey = `${key}:${aiTranslation}`;

    // 检查缓存
    if (this.qualityCache.has(cacheKey)) {
      return this.qualityCache.get(cacheKey)!;
    }

    const issues: QualityIssue[] = [];
    let score = 100;

    // 基础质量检查
    if (aiTranslation.trim().length === 0) {
      issues.push({
        type: 'length',
        severity: 'critical',
        message: 'Translation is empty',
        suggestion: 'Provide a non-empty translation',
      });
      score -= 50;
    }

    // 占位符检查
    const aiPlaceholders = this.extractPlaceholders(aiTranslation);
    if (humanTranslation) {
      const humanPlaceholders = this.extractPlaceholders(humanTranslation);

      if (JSON.stringify(aiPlaceholders.sort()) !== JSON.stringify(humanPlaceholders.sort())) {
        issues.push({
          type: 'placeholder',
          severity: 'high',
          message: 'Placeholder mismatch between AI and human translation',
          suggestion: 'Ensure all placeholders are preserved',
        });
        score -= 20;
      }
    }

    // 长度比例检查
    if (humanTranslation) {
      const lengthRatio = aiTranslation.length / humanTranslation.length;
      if (lengthRatio > 3 || lengthRatio < 0.3) {
        issues.push({
          type: 'length',
          severity: 'medium',
          message: `Translation length ratio is unusual: ${lengthRatio.toFixed(2)}`,
          suggestion: 'Review translation for completeness and accuracy',
        });
        score -= 10;
      }
    }

    // 术语一致性检查
    const terminologyIssues = await this.checkTerminologyConsistency(key, aiTranslation);
    issues.push(...terminologyIssues);
    score -= terminologyIssues.length * 5;

    const qualityScore: QualityScore = {
      score: Math.max(0, score),
      confidence: this.calculateConfidence(issues),
      issues,
      suggestions: this.generateSuggestions(issues),
    };

    // 缓存结果
    this.qualityCache.set(cacheKey, qualityScore);

    return qualityScore;
  }

  /**
   * 验证翻译一致性
   */
  async validateTranslationConsistency(
    translations: Record<string, string>
  ): Promise<ValidationReport> {
    const issues: QualityIssue[] = [];
    let totalScore = 0;
    let validTranslations = 0;

    for (const [key, translation] of Object.entries(translations)) {
      if (!translation || translation.trim().length === 0) {
        issues.push({
          type: 'consistency',
          severity: 'high',
          message: `Missing translation for key: ${key}`,
        });
        continue;
      }

      // 检查占位符一致性
      const placeholders = this.extractPlaceholders(translation);
      const expectedPlaceholders = this.getExpectedPlaceholders(key);

      if (JSON.stringify(placeholders.sort()) !== JSON.stringify(expectedPlaceholders.sort())) {
        issues.push({
          type: 'placeholder',
          severity: 'medium',
          message: `Placeholder inconsistency in key: ${key}`,
        });
      }

      validTranslations++;
      totalScore += 100; // 基础分数
    }

    const averageScore = validTranslations > 0 ? totalScore / validTranslations : 0;
    const finalScore = Math.max(0, averageScore - (issues.length * 5));

    return {
      isValid: issues.filter(issue => issue.severity === 'critical' || issue.severity === 'high').length === 0,
      score: finalScore,
      issues,
      recommendations: this.generateRecommendations(issues),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 生成质量报告
   */
  async generateQualityReport(): Promise<QualityReport> {
    const byLocale: Record<Locale, QualityScore> = {} as Record<Locale, QualityScore>;
    const allIssues: QualityIssue[] = [];

    // 为每个语言生成质量评分
    for (const locale of this.config.locales) {
      const localeTranslations = this.translations[locale] || {};
      const localeIssues: QualityIssue[] = [];
      let localeScore = 100;

      // 检查翻译完整性
      const totalKeys = this.getTotalTranslationKeys();
      const translatedKeys = Object.keys(this.flattenTranslations(localeTranslations)).length;
      const completeness = translatedKeys / totalKeys;

      if (completeness < 0.95) {
        localeIssues.push({
          type: 'consistency',
          severity: 'medium',
          message: `Translation completeness is ${(completeness * 100).toFixed(1)}%`,
        });
        localeScore -= (1 - completeness) * 50;
      }

      byLocale[locale] = {
        score: Math.max(0, localeScore),
        confidence: this.calculateConfidence(localeIssues),
        issues: localeIssues,
        suggestions: this.generateSuggestions(localeIssues),
      };

      allIssues.push(...localeIssues);
    }

    // 计算整体评分
    const overallScore = Object.values(byLocale).reduce((sum, score) => sum + score.score, 0) / this.config.locales.length;

    return {
      overall: {
        score: overallScore,
        confidence: this.calculateConfidence(allIssues),
        issues: allIssues,
        suggestions: this.generateSuggestions(allIssues),
      },
      byLocale,
      trends: await this.getQualityTrends(),
      recommendations: this.generateRecommendations(allIssues),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 提取占位符
   */
  private extractPlaceholders(text: string): string[] {
    const matches = text.match(/\{[^}]+\}/g) || [];
    return matches.map(match => match.slice(1, -1));
  }

  /**
   * 获取预期占位符
   */
  private getExpectedPlaceholders(key: string): string[] {
    // 从默认语言获取预期占位符
    const defaultTranslation = this.getNestedValue(this.translations[this.config.defaultLocale], key);
    if (typeof defaultTranslation === 'string') {
      return this.extractPlaceholders(defaultTranslation);
    }
    return [];
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 扁平化翻译对象
   */
  private flattenTranslations(obj: any, prefix = ''): Record<string, string> {
    const result: Record<string, string> = {};

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;

      if (typeof value === 'object' && value !== null) {
        Object.assign(result, this.flattenTranslations(value, fullKey));
      } else if (typeof value === 'string') {
        result[fullKey] = value;
      }
    }

    return result;
  }

  /**
   * 获取总翻译键数
   */
  private getTotalTranslationKeys(): number {
    const defaultTranslations = this.translations[this.config.defaultLocale] || {};
    return Object.keys(this.flattenTranslations(defaultTranslations)).length;
  }

  /**
   * 检查术语一致性
   */
  private async checkTerminologyConsistency(_key: string, _translation: string): Promise<QualityIssue[]> {
    // 这里可以实现术语一致性检查逻辑
    // 例如检查品牌名称、技术术语等是否一致
    return [];
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(issues: QualityIssue[]): number {
    const criticalIssues = issues.filter(issue => issue.severity === 'critical').length;
    const highIssues = issues.filter(issue => issue.severity === 'high').length;

    let confidence = 1.0;
    confidence -= criticalIssues * 0.3;
    confidence -= highIssues * 0.2;
    confidence -= issues.length * 0.05;

    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * 生成建议
   */
  private generateSuggestions(issues: QualityIssue[]): string[] {
    const suggestions = new Set<string>();

    issues.forEach(issue => {
      if (issue.suggestion) {
        suggestions.add(issue.suggestion);
      }
    });

    return Array.from(suggestions);
  }

  /**
   * 生成推荐
   */
  private generateRecommendations(issues: QualityIssue[]): string[] {
    const recommendations: string[] = [];

    const criticalCount = issues.filter(issue => issue.severity === 'critical').length;
    const highCount = issues.filter(issue => issue.severity === 'high').length;

    if (criticalCount > 0) {
      recommendations.push(`Address ${criticalCount} critical translation issues immediately`);
    }

    if (highCount > 0) {
      recommendations.push(`Review and fix ${highCount} high-priority translation issues`);
    }

    if (issues.length > 10) {
      recommendations.push('Consider implementing automated translation quality checks');
    }

    return recommendations;
  }

  /**
   * 获取质量趋势
   */
  private async getQualityTrends(): Promise<QualityTrend[]> {
    // 这里可以实现质量趋势分析
    // 例如从历史数据中获取质量变化趋势
    return [];
  }
}
